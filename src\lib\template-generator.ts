// Template generation functions with real implementation
// Based on the backend template system from backend/src/test/templates.ts

import { parseTemplateText } from "./template-text-parser";
import { evaluateTree } from "./template-tree-evaluator";
import { parseTree } from "./template-tree-parser";

export interface TemplateContext {
  ordinal: number | null;
  service: string | null;
  incoterms: string | null;
  from: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
    isPickupRequired: boolean;
  };
  to: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
    isDeliveryRequired: boolean;
  };
  goods: {
    description: string | null;
    hsCodes: string | null;
    cost: number;
    currency: string | null;
  };
  services: {
    additional: string[];
    dangerous: string[];
  };
  packages: {
    quantity: number;
    length: number | null;
    width: number | null;
    height: number | null;
    volume: number | null;
    weight: number | null;
    type: string | null;
    isStackable: boolean;
  }[];
  total: {
    quantity: number;
    weight: number;
    volume: number;
    chargeableWeight: number;
  };
}

type TemplateData = {
  // Basic information
  "ordinal": string;
  "service": string;
  "incoterms": string;

  // Origin (from) information
  "from.country": string;
  "from.city": string;
  "from.address": string;
  "from.zipcode": string;
  "pickup.required": boolean;

  // Destination (to) information
  "to.country": string;
  "to.city": string;
  "to.address": string;
  "to.zipcode": string;
  "delivery.required": boolean;

  // Goods information
  "goods.description": string;
  "goods.hsCodes": string;
  "goods.cost": number;
  "goods.currency": string;

  // Services information
  "services.additional": string;
  "services.dangerous": string;

  // Total information
  "total.quantity": string;
  "total.weight": string;
  "total.volume": string;
  "total.chargeableWeight": string;

  // Packages for loop processing
  packages: {
    quantity: number;
    length: number;
    width: number;
    height: number;
    volume: number;
    weight: number;
    type: string;
    stackable: boolean;
  }[] | undefined;
}

export function fillTemplate2(template: string, data: TemplateData): string {
  {
    const packagesTagIndex = template.indexOf("{packages}");
    const isHasPackagesTag = packagesTagIndex !== -1;
  
    if (isHasPackagesTag && template.indexOf("{packages}", packagesTagIndex + 1) !== -1) {
      throw new Error("Template cannot have multiple {packages} tags");
    }
  }

  const parts = parseTemplateText(template);

  const partsWithEvaluatedPlain = parts
    .map(part => {
      if (part.type === "text") {
        return part;
      }

      if (
        part.type === "token"
        && (
          part.value === "packages"
          || part.value === "/packages"
          || part.value.startsWith("package.")
        )
      ) {
        return part;
      }

      const tree = parseTree(`{${part.value}}`);

      return {
        type: "text" as const,
        value: evaluateTree(data, tree) ?? "",
      };
    });

  let result = partsWithEvaluatedPlain;

  const packagesTagIndex = result
    .findIndex(part => part.type === "token" && part.value === "packages");

  if (packagesTagIndex !== -1) {
    const packagesClosingTagIndex = result
      .findIndex(part => part.type === "token" && part.value === "/packages");

    if (packagesClosingTagIndex === -1) {
      throw new Error("Template must have a closing {/packages} tag after {packages}");
    }

    if (packagesTagIndex > packagesClosingTagIndex) {
      throw new Error("Template must have a closing {/packages} tag after {packages}");
    }

    const packagesParts = result.slice(packagesTagIndex + 1, packagesClosingTagIndex);

    if (packagesParts[0]?.type === "text") {
      const value = packagesParts[0].value;
      const trimmedValue = value.trimStart();

      if (value.length - trimmedValue.length > value.indexOf("\n") + 1) {
        packagesParts[0] = {
          type: "text" as const,
          value: value.slice(value.indexOf("\n") + 1),
        }
      } else {
        packagesParts[0] = {
          type: "text" as const,
          value: trimmedValue,
        }
      }
    }

    const evaluatedPackagesParts = data.packages?.map(packageData => {
      const data = Object.fromEntries(
        Object.entries(packageData).map(([key, value]) => ["package." + key, value]),
      );

      return packagesParts.map(part => {
        if (part.type === "text") {
          return part;
        }

        return {
          type: "text" as const,
          value: evaluateTree(data, parseTree(`{${part.value}}`)) ?? "",
        };
      });
    });

    result = [
      ...result.slice(0, packagesTagIndex),
      ...evaluatedPackagesParts?.flat() ?? [],
      ...result.slice(packagesClosingTagIndex + 1),
    ]
  }

  return result.map(part => part.value).join("");
}

function round2(value: number) {
  return Math.round(value * 100) / 100;
}

export function getTemplateData(ctx: TemplateContext, withPackages = false): TemplateData {
  return {
    // Basic information
    "ordinal": ctx.ordinal ? `IN-${String(ctx.ordinal).padStart(3, "0")}` : "",
    "service": ctx.service || "",
    "incoterms": ctx.incoterms || "",

    // Origin (from) information
    "from.country": ctx.from.country || "",
    "from.city": ctx.from.city || "",
    "from.address": ctx.from.address || "",
    "from.zipcode": ctx.from.zipcode || "",
    "pickup.required": ctx.from.isPickupRequired,

    // Destination (to) information
    "to.country": ctx.to.country || "",
    "to.city": ctx.to.city || "",
    "to.address": ctx.to.address || "",
    "to.zipcode": ctx.to.zipcode || "",
    "delivery.required": ctx.to.isDeliveryRequired,

    // Goods information
    "goods.description": ctx.goods.description || "",
    "goods.hsCodes": ctx.goods.hsCodes || "",
    "goods.cost": ctx.goods.cost,
    "goods.currency": ctx.goods.currency || "",

    // Services information
    "services.additional": ctx.services.additional.join(", "),
    "services.dangerous": ctx.services.dangerous.join(", "),

    // Total information
    "total.quantity": String(ctx.total.quantity),
    "total.weight": String(round2(ctx.total.weight)),
    "total.volume": String(round2(ctx.total.volume)),
    "total.chargeableWeight": String(round2(ctx.total.chargeableWeight)),

    // Packages for loop processing
    packages: withPackages
      ? ctx.packages.map(pkg => ({
        quantity: pkg.quantity || 0,
        length: pkg.length || 0,
        width: pkg.width || 0,
        height: pkg.height || 0,
        volume: pkg.volume || 0,
        weight: pkg.weight || 0,
        type: pkg.type || "",
        stackable: pkg.isStackable,
      }))
      : undefined,
  };
}

export function generateResultTitle(template: string, ctx: TemplateContext): string {
  const templateData = getTemplateData(ctx, false);

  // return fillTemplate(template, templateData).trim();
  return fillTemplate2(template, templateData).trim();
}

export function generateResultContent(template: string, ctx: TemplateContext): string {
  const templateData = getTemplateData(ctx, true);

  // return fillTemplate(template, templateData).trim();
  return fillTemplate2(template, templateData).trim();
}

export type TemplatePreset = {
  name: string;
  content: string;
};

export type TemplateTag = {
  tag: string;
  description: string;
};

export const TITLE_TEMPLATE_PRESETS = [
  {
    name: "Pickup address",
    content: '{{pickup.required AND {from.city OR from.address}} ? {"Pickup address: " + from.country + {", ", from.city} + {", ", from.zipcode} + {", ", from.address}} : ""}',
  },
  {
    name: "Delivery address",
    content: '{{delivery.required AND {to.city OR to.address}} ? {"Delivery address: " + to.country + {", ", to.city} + {", ", to.zipcode} + {", ", to.address}} : ""}',
  },
  {
    name: "Itinerary",
    content: '{"Itinerary: " + {from.city ?? from.country} + " – " + {to.city ?? to.country}}',
  },
  {
    name: "Total dimensions",
    content: '{"Totally: ", total.quantity, " pcs, ", total.weight, " kgs, ", total.volume, " cbm"}',
  },
  {
    name: "Detailed dimensions",
    content: `
{total.quantity ? "Dimensions" : ""}
{packages}
{package.quantity} pcs., {package.length}*{package.width}*{package.height} cm {package.stackable ? "" : "- NOT stackable"} 
{/packages}
`.trim(),
  },
  {
    name: "Type of goods",
    content: '{services.dangerous ? {"Contains dangerous goods: " + services.dangerous} : "General cargo"}',
  },
  {
    name: "Full description of goods",
    content: `
{goods.description ? "Description of goods: " + goods.description : ""}
{goods.hsCodes ? "HS codes: " + goods.hsCodes : ""}
{goods.cost ? "Cost: " + goods.cost + " " + goods.currency : ""}
`.trim(),
  },
] satisfies TemplatePreset[];

export const CONTENT_TEMPLATE_PRESETS = [
  ...TITLE_TEMPLATE_PRESETS,
] satisfies TemplatePreset[];

// Template tags for title and content fields
export const TITLE_TEMPLATE_TAGS = [
  // Basic information
  {
    tag: 'ordinal',
    description: 'Request ordinal number (e.g., IN-001)',
  },
  {
    tag: 'service',
    description: 'Requested service type (e.g., Air freight, Sea freight)',
  },
  {
    tag: 'incoterms',
    description: 'Incoterms (e.g., EXW, FOB, CIF)',
  },

  // Origin (from) information
  {
    tag: 'from.country',
    description: 'Origin country name',
  },
  {
    tag: 'from.city',
    description: 'Origin city name',
  },
  {
    tag: 'from.address',
    description: 'Complete pickup address including city, zipcode, and country',
  },
  {
    tag: 'from.zipcode',
    description: 'Origin zipcode',
  },

  // Destination (to) information
  {
    tag: 'to.country',
    description: 'Destination country name',
  },
  {
    tag: 'to.city',
    description: 'Destination city name',
  },
  {
    tag: 'to.address',
    description: 'Complete delivery address including city, zipcode, and country',
  },
  {
    tag: 'to.zipcode',
    description: 'Destination zipcode',
  },

  // Goods information
  {
    tag: 'goods.description',
    description: 'Description of goods being shipped',
  },
  {
    tag: 'goods.hsCodes',
    description: 'Harmonized System codes for customs',
  },
  {
    tag: 'goods.cost',
    description: 'Cost of goods (e.g., 1500)',
  },
  {
    tag: 'goods.currency',
    description: 'Currency code (e.g., USD, EUR)',
  },

  // Services information
  {
    tag: 'services.additional',
    description: 'Comma-separated list of additional services',
  },
  {
    tag: 'services.dangerous',
    description: 'Comma-separated list of dangerous goods',
  },

  // Total information
  {
    tag: 'total.quantity',
    description: 'Total quantity (e.g., 25)',
  },
  {
    tag: 'total.weight',
    description: 'Total weight (e.g., 150.50)',
  },
  {
    tag: 'total.volume',
    description: 'Total volume (e.g., 2.45)',
  },
  {
    tag: 'total.chargeableWeight',
    description: 'Total chargeable weight (e.g., 150.54)',
  },
];

export const CONTENT_TEMPLATE_TAGS = [
  ...TITLE_TEMPLATE_TAGS,

  // Package loop tags
  {
    tag: 'packages',
    description: 'Start of package loop - use with {/packages}. All package.* tags must be used inside {packages} loop',
  },
  {
    tag: '/packages',
    description: 'End of package loop',
  },
  {
    tag: 'package.quantity',
    description: 'Package quantity (use inside {packages} loop)',
  },
  {
    tag: 'package.length',
    description: 'Package length in cm (use inside {packages} loop)',
  },
  {
    tag: 'package.width',
    description: 'Package width in cm (use inside {packages} loop)',
  },
  {
    tag: 'package.height',
    description: 'Package height in cm (use inside {packages} loop)',
  },
  {
    tag: 'package.volume',
    description: 'Package volume in cbm (use inside {packages} loop)',
  },
  {
    tag: 'package.weight',
    description: 'Package weight in kg (use inside {packages} loop)',
  },
  {
    tag: 'package.type',
    description: 'Package type (use inside {packages} loop)',
  },
  {
    tag: 'package.stackable',
    description: 'Package stackable (use inside {packages} loop)',
  },
];
